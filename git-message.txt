feat: Add drag-and-drop service reordering in admin panel

- Add sort_order field to services table with migration
- Update Service model to include sort_order in fillable array
- Implement drag-and-drop interface using SortableJS library
- Add AJAX endpoint for real-time service reordering
- Update frontend to display services in custom order
- Include visual feedback and error handling
- Add seeder for existing services sort_order initialization
- Create comprehensive documentation for the feature
- Add error handling and user feedback for validation failures
- Updated resources/views/layouts/front.blade.php asset references
- Changed header__content__venor to header__content__darkmoon
- Changed header__actions__venor to header__actions__darkmoon
- Changed header__menu__venor to header__menu__darkmoon
- Updated venor-animate-border to darkmoon-animate-border
- Modified slider-venor-section to slider-darkmoon-section
- Updated venor-team to darkmoon-team classes
- Changed venor-price-box to darkmoon-price-box

Translation Namespace Fix:
- Renamed language files from niva-backend.php to meridian-backend.php
- Updated all template files to use meridian-backend translations
- Fixed translation keys in all view files
- Cleared application cache to refresh translations

Image Path Fix:
- Fixed broken image loading in admin edit forms
- Corrected image paths from /public/images/media/ to /images/media/
- Fixed image paths from /public/img/ to /img/
- Updated all affected templates for proper image display

Language Files Renamed:
- resources/lang/en/niva-backend.php → meridian-backend.php
- resources/lang/pt/niva-backend.php → meridian-backend.php
- resources/lang/عربى/niva-backend.php → meridian-backend.php

Documentation Updates:
- Updated public/docs/index.html references
- Renamed venor-logo.svg to darkmoon-logo.svg
- Updated installation documentation
- Created comprehensive development backlog in docs/development-backlog.md

Files Modified:
- public/css/front/darkmoon.css (renamed from venor.css)
- public/js/front/darkmoon.js (renamed from venor.js)
- resources/views/layouts/front.blade.php
- resources/views/home.blade.php
- resources/views/project.blade.php
- resources/views/pricing.blade.php
- resources/views/about.blade.php
- resources/views/search.blade.php
- resources/views/page/page-edit.blade.php
- resources/views/project/project-edit.blade.php
- resources/views/post/post-edit.blade.php
- resources/views/language/language-edit.blade.php
- resources/views/slider/slider-edit.blade.php
- resources/views/article.blade.php
- resources/views/settings/contact/contact-edit.blade.php
- resources/views/settings/portfolio/portfolio-edit.blade.php
- public/docs/index.html
- public/docs/assets/images/darkmoon-logo.svg (renamed)
- docs/development-backlog.md (created)
- All language files in resources/lang/*/meridian-backend.php
